{"name": "cricket-booking-client", "private": true, "version": "1.0.0", "description": "Cricket Ground & Room Booking System Frontend", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^6.28.0", "@tanstack/react-query": "^5.59.0", "zustand": "^5.0.1", "firebase": "^10.14.0", "axios": "^1.7.7", "date-fns": "^4.1.0", "react-hook-form": "^7.53.0", "@hookform/resolvers": "^3.9.0", "zod": "^3.23.8", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4", "lucide-react": "^0.454.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-calendar": "^1.1.0", "@radix-ui/react-popover": "^1.1.2", "react-big-calendar": "^1.15.0", "recharts": "^2.12.7"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-big-calendar": "^1.8.12", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.0", "tailwindcss": "^3.4.14", "autoprefixer": "^10.4.20", "postcss": "^8.4.49"}}